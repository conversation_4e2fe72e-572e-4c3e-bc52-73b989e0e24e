package com.ximalaya.galaxy.business.repo.enums

import com.ximalaya.galaxy.business.common.enums.Enums

/**
 *<AUTHOR>
 *@create 2025-05-28 10:43
 */
enum class GalaxyToolType(
  val code: Int,
  val desc: String
) {

  DIFY(1, "Dify"),
  ;

  fun equalsCode(dbCode: Int?) =
    when (dbCode) {
      null -> false
      else -> code == dbCode
    }

  companion object {

    @JvmStatic
    fun parseCode(code: Int?): BlockType {
      return Enums.parseNotNull(
        BlockType.values(),
        BlockType::code,
        code,
        "Enum not support BlockType: $code"
      )
    }

  }

}