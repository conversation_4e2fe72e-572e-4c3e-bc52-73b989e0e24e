package com.ximalaya.galaxy.business.repo.service.impl

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.ximalaya.galaxy.business.repo.entity.GalaxyToolEntity
import com.ximalaya.galaxy.business.repo.enums.GalaxyToolType
import com.ximalaya.galaxy.business.repo.enums.LogicDeleted
import com.ximalaya.galaxy.business.repo.mapper.GalaxyToolMapper
import com.ximalaya.galaxy.business.repo.service.GalaxyToolService
import org.springframework.stereotype.Repository

/**
 *<AUTHOR>
 *@create 2025-05-28 10:39
 */
@Repository
class GalaxyToolServiceImpl : ServiceImpl<GalaxyToolMapper, GalaxyToolEntity>(), GalaxyToolService {

  override fun selectByToolType(toolType: GalaxyToolType): List<GalaxyToolEntity> {
    return ktQuery().eq(GalaxyToolEntity::toolType, toolType.code)
      .eq(GalaxyToolEntity::deletedAt, LogicDeleted.SAVE.getCode())
      .list()
  }

}
