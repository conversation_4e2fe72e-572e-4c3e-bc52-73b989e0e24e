package com.ximalaya.galaxy.business.business.boss

import com.ximalaya.galaxy.business.business.boss.vo.SessionVo
import com.ximalaya.galaxy.business.business.boss.vo.StartSessionJobVo
import com.ximalaya.galaxy.business.repo.enums.PhaseState
import com.ximalaya.galaxy.business.service.vo.AgentContentProtocol
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter

/**
 *<AUTHOR>
 *@create 2025-05-20 10:52
 */
interface BizGalaxyBoss {

  fun createSessionAndFirstPhase(uid: Long, sessionName: String): Pair<Long, Long>

  fun createSession(uid: Long, sessionName: String): Long

  fun createPhase(uid: Long, sessionCode: Long, phaseName: String): Long

  fun updatePhaseState(sessionCode: Long, phaseCode: Long, phaseState: PhaseState): Boolean

  fun getSessionPhases(uid: Long, sessionCode: Long): SessionVo

  fun connectSessionPhase(emitter: SseEmitter, uid: Long, sessionCode: Long, phaseCode: Long)

  fun startSessionJob(emitter: SseEmitter, startVo: StartSessionJobVo)

  fun sendSseWriteBackMessage(agentMessage: AgentContentProtocol)

  fun startDifyJob(uid: Long, sessionCode: Long, phaseCode: Long, toolId: Long, inputs: Map<String, String?>?)

}