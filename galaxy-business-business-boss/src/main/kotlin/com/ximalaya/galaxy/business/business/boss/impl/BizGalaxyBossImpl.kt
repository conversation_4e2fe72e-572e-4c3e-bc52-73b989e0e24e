package com.ximalaya.galaxy.business.business.boss.impl

import com.ximalaya.galaxy.business.business.BusinessComponent
import com.ximalaya.galaxy.business.business.boss.BizGalaxyBoss
import com.ximalaya.galaxy.business.business.boss.sse.SseWriteBackSender
import com.ximalaya.galaxy.business.business.boss.vo.SessionVo
import com.ximalaya.galaxy.business.business.boss.vo.StartSessionJobVo
import com.ximalaya.galaxy.business.common.ALBUM_OUTLINE
import com.ximalaya.galaxy.business.common.MESSAGE_SEPARATOR
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.support.GalaxyAsserts
import com.ximalaya.galaxy.business.common.support.SplitHelper
import com.ximalaya.galaxy.business.repo.entity.GalaxyBlockEntity
import com.ximalaya.galaxy.business.repo.entity.GalaxyPhaseEntity
import com.ximalaya.galaxy.business.repo.entity.GalaxySessionEntity
import com.ximalaya.galaxy.business.repo.enums.BlockType
import com.ximalaya.galaxy.business.repo.enums.LogicDeleted
import com.ximalaya.galaxy.business.repo.enums.PhaseState
import com.ximalaya.galaxy.business.repo.enums.SessionState
import com.ximalaya.galaxy.business.repo.service.GalaxyBlockService
import com.ximalaya.galaxy.business.repo.service.GalaxyPhaseService
import com.ximalaya.galaxy.business.repo.service.GalaxySessionService
import com.ximalaya.galaxy.business.service.enums.AgentContentType
import com.ximalaya.galaxy.business.service.support.RedisLockHelper
import com.ximalaya.galaxy.business.service.support.getAgentMessageCacheKey
import com.ximalaya.galaxy.business.service.vo.*
import com.ximalaya.galaxy.business.vo.ResultVo
import com.ximalaya.galaxy.business.worker.api.converter.StartSessionJobResponseConverter
import com.ximalaya.galaxy.business.worker.api.thrift.GalaxyBusinessWorkerJobService
import com.ximalaya.hot.track.service.vo.DifyRequestVo
import com.ximalaya.hot.track.service.vo.DifyResponseVo
import mu.KotlinLogging
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.redisson.api.RedissonClient
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter
import java.time.LocalDateTime
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit

/**
 *<AUTHOR>
 *@create 2025-05-20 10:54
 */
@BusinessComponent
class BizGalaxyBossImpl(
  private val sessionService: GalaxySessionService,
  private val phaseService: GalaxyPhaseService,
  private val blockService: GalaxyBlockService,
  private val workerJobIfaceService: GalaxyBusinessWorkerJobService.Iface,
  private val redissonClient: RedissonClient,
  private val stringRedisTemplate: StringRedisTemplate,
  private val sseWriteBackSender: SseWriteBackSender,
) : BizGalaxyBoss {

  private var sseMap = ConcurrentHashMap<Pair<Long, Long>, SseEmitter>()

  @Transactional(rollbackFor = [Throwable::class])
  override fun createSessionAndFirstPhase(uid: Long, sessionName: String): Pair<Long, Long> {
    val sessionCode = createSession(uid, sessionName)
    val phaseCode = createPhase(uid, sessionCode, ALBUM_OUTLINE)
    return Pair(sessionCode, phaseCode)
  }

  override fun createSession(uid: Long, sessionName: String): Long {
    val now = LocalDateTime.now()

    val sessionEntity = GalaxySessionEntity().apply {
      this.uid = uid
      this.sessionName = sessionName
      this.sessionState = SessionState.INIT.code
      this.deletedAt = LogicDeleted.SAVE.getCode()
      this.createTime = now
      this.updateTime = now
    }

    GalaxyAsserts.assertTrue(sessionService.save(sessionEntity), ErrorCode.SESSION_ERROR, "创建会话失败")
    // sessionCode
    return sessionEntity.id!!
  }

  override fun createPhase(uid: Long, sessionCode: Long, phaseName: String): Long {
    val sessionEntity = sessionService.selectBySessionCode(sessionCode)
    GalaxyAsserts.assertNotNull(sessionEntity, ErrorCode.SESSION_ERROR, "会话不存在")
    GalaxyAsserts.assertEquals(sessionEntity!!.uid, uid, ErrorCode.SESSION_ERROR, "这里不是您的会话哦~")

    val now = LocalDateTime.now()

    val phaseEntity = GalaxyPhaseEntity().apply {
      this.sessionCode = sessionCode
      this.phaseName = phaseName
      this.phaseState = PhaseState.INIT.code
      this.deletedAt = LogicDeleted.SAVE.getCode()
      this.createTime = now
      this.updateTime = now
    }

    GalaxyAsserts.assertTrue(phaseService.save(phaseEntity), ErrorCode.PHASE_ERROR, "创建阶段失败")
    // phaseCode
    return phaseEntity.id!!
  }

  override fun updatePhaseState(sessionCode: Long, phaseCode: Long, phaseState: PhaseState): Boolean {
    return phaseService.ktUpdate()
      .eq(GalaxyPhaseEntity::sessionCode, sessionCode)
      .eq(GalaxyPhaseEntity::phaseCode, phaseCode)
      .set(GalaxyPhaseEntity::phaseState, phaseState.code)
      .set(GalaxyPhaseEntity::updateTime, LocalDateTime.now())
      .update()
  }

  override fun getSessionPhases(uid: Long, sessionCode: Long): SessionVo {
    val sessionEntity = sessionService.selectBySessionCode(sessionCode)
    GalaxyAsserts.assertNotNull(sessionEntity, ErrorCode.SESSION_ERROR, "会话不存在")
    GalaxyAsserts.assertEquals(sessionEntity!!.uid, uid, ErrorCode.SESSION_ERROR, "这里不是您的会话哦~")

    val phaseEntities = phaseService.selectBySessionCode(sessionCode)
    return SessionVo.of(sessionEntity, phaseEntities)
  }

  override fun connectSessionPhase(emitter: SseEmitter, uid: Long, sessionCode: Long, phaseCode: Long) {
    // todo uid需要和sessionCode对应

    emitter.onCompletion {
      sseMap.remove(Pair(sessionCode, phaseCode))
    }
    // todo redis拦截 sessionCode, phaseCode只能在一台

    sseMap.computeIfAbsent(Pair(sessionCode, phaseCode)) {
      emitter
    }

    // 回放
    val redisKey = RedisLockHelper.getSseWriteBackLockKey(sessionCode, phaseCode)
    val rwLock = redissonClient.getReadWriteLock(redisKey)
    val writeLock = rwLock.writeLock()
    try {
      if (!writeLock.tryLock(10, TimeUnit.SECONDS)) {
        return
      }

      val messages = stringRedisTemplate.opsForValue().get(getAgentMessageCacheKey(sessionCode, phaseCode))
      if (StringUtils.isNotBlank(messages)) {
        emitPhaseMessageFromCache(emitter, messages!!)
      }

      // 从db查
      val blocks = blockService.ktQuery().eq(GalaxyBlockEntity::sessionCode, sessionCode)
        .eq(GalaxyBlockEntity::phaseCode, phaseCode)
        .eq(GalaxyBlockEntity::deletedAt, LogicDeleted.SAVE.getCode())
        .list()

      if (CollectionUtils.isEmpty(blocks)) {
        // todo 报错
      }
      emitPhaseMessageFormDB(emitter, blocks)

    } finally {
      if (writeLock.isLocked && writeLock.isHeldByCurrentThread) {
        writeLock.unlock()
      }
    }
  }

  private fun emitPhaseMessageFromCache(emitter: SseEmitter, redisCache: String) {
    SplitHelper.deserialize(redisCache, MESSAGE_SEPARATOR, AgentContentProtocol::parseJson).forEach {
      emitter.send(ResultVo.ok(it))
      // 如果是stop的
      if (it._type in setOf(AgentContentType.FINISH, AgentContentType.ERROR, AgentContentType.EXCEPTION)) {
        emitter.complete()
        return
      }
    }
  }

  private fun emitPhaseMessageFormDB(emitter: SseEmitter, blocks: List<GalaxyBlockEntity>) {
    for (block in blocks) {
      when (BlockType.parseCode(block.blockType)) {
        BlockType.USER -> {
          emitter.send(
            ResultVo.ok(
              AgentContentBlockUser().apply {
                this.blockCode = block.blockCode
                this.content = block.content
              }
            ))
        }


        BlockType.TEXT -> {
          emitter.send(ResultVo.ok(AgentContentBlockText().apply {
            this.blockCode = block.blockCode
          }))
          emitter.send(
            ResultVo.ok(AgentContentText.parseJson(block.content!!))
          )
        }

        BlockType.MCP_TOOL_CALL -> {
          emitter.send(ResultVo.ok(AgentContentBlockToolCall().apply {
            this.blockCode = block.blockCode
          }))
          emitter.send(
            ResultVo.ok(AgentContentToolCall.parseJson(block.content!!))
          )
        }

        BlockType.MCP_TOOL_RESULT -> {
          emitter.send(ResultVo.ok(AgentContentBlockToolResult().apply {
            this.blockCode = block.blockCode
          }))
          emitter.send(
            ResultVo.ok(AgentContentToolResult.parseJson(block.content!!))
          )
        }

        BlockType.DIFY_CALL -> {
          emitter.send(ResultVo.ok(AgentContentBlockDifyCall().apply {
            this.blockCode = block.blockCode
            this.args = DifyRequestVo.parseJson(block.content!!)
          }))
        }

        BlockType.DIFY_RESULT -> {
          emitter.send(ResultVo.ok(AgentContentBlockDifyResult().apply {
            this.blockCode = block.blockCode
            this.result = DifyResponseVo.parseJson(block.content!!)
          }))
        }

        else -> emitter.send(ResultVo.failed<Unit>(ErrorCode.SESSION_ERROR, "会话消息发生错误"))
      }

      emitter.complete()
    }
  }

  override fun startSessionJob(emitter: SseEmitter, startVo: StartSessionJobVo) {
    emitter.onCompletion {
      sseMap.remove(Pair(startVo.sessionCode, startVo.phaseCode))
    }

    // todo redis拦截 sessionCode, phaseCode只能在一台

    sseMap.computeIfAbsent(Pair(startVo.sessionCode!!, startVo.phaseCode!!)) {
      emitter
    }

    // 发送worker
    val redisKey = RedisLockHelper.getPhaseOngoingLockKey(startVo.sessionCode!!, startVo.phaseCode!!)
    val ongoingBucket = redissonClient.getBucket<Long>(redisKey)
    if (ongoingBucket.isExists) {
      logger.warn("任务重复执行, Session: {}, Phase: {}", startVo.sessionCode, startVo.phaseCode)
      return
    }

    val thriftResponse = workerJobIfaceService.startSessionJob(startVo.toStartSessionJobRequest())

    val response = StartSessionJobResponseConverter.transform(thriftResponse)

    if (response.code != 200) {
      logger.error("启动任务失败, Session: {}, Phase: {}, Code: {}, Message: {}", startVo.sessionCode, startVo.phaseCode, response.code, response.message)
      sseWriteBackSender.sendMessage(AgentContentGalaxyException().apply {
        this.sessionCode = startVo.sessionCode
        this.phaseCode = startVo.phaseCode
        this.errorCode = ErrorCode.SESSION_ERROR
        this.errorMessage = response.message
      })
      return
    }
  }

  override fun sendSseWriteBackMessage(agentMessage: AgentContentProtocol) {
    val redisKey = RedisLockHelper.getSseWriteBackLockKey(agentMessage.sessionCode!!, agentMessage.phaseCode!!)
    val rwLock = redissonClient.getReadWriteLock(redisKey)
    val readLock = rwLock.readLock()
    try {
      if (!readLock.tryLock(3, TimeUnit.SECONDS)) {
        return
      }

      if (sseMap.containsKey(agentMessage.toPair())) {
        sseMap[agentMessage.toPair()]?.let { emitter ->
          emitter.send(ResultVo.ok(agentMessage))

          // 如果是stop的
          if (agentMessage._type in setOf(AgentContentType.FINISH, AgentContentType.ERROR, AgentContentType.EXCEPTION)) {
            emitter.complete()
          }
        }
      }

    } finally {
      if (readLock.isLocked && readLock.isHeldByCurrentThread) {
        readLock.unlock()
      }
    }
  }

  override fun callDify(uid: Long, sessionCode: Long, phaseCode: Long, toolId: Long, inputs: Map<String, String?>?) {
    val sessionEntity = sessionService.selectBySessionCode(sessionCode)
    GalaxyAsserts.assertNotNull(sessionEntity, ErrorCode.SESSION_ERROR, "会话不存在")
    GalaxyAsserts.assertEquals(sessionEntity!!.uid, uid, ErrorCode.SESSION_ERROR, "这里不是您的会话哦~")
  }

  companion object {

    private val logger = KotlinLogging.logger { }

  }

}